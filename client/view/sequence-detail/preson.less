.cadre-management {
  width: 100%;
  height: 600px;
  display: flex;

  .ant-select-selection {
    width: 100%;
  }

  .left {
    display: flex;
    width: 210px;
    background: #fff;
    flex-direction: column;

    .left-header {
      width: 100%;
      height: 50px;
      background: #f1f5f8;

      text h2 {
        height: 50px;
        line-height: 50px;
        background: #fff;
        width: 100px;
        text-align: center;
      }

      .ant-tabs {
        height: 40px;
      }

      .ant-tabs-tab {
        margin: 0;
        height: 40px;
        line-height: 38px;
        padding: 0 16px;
        border: 1px solid #e8e8e8;
        border-radius: 4px 4px 0 0;
        margin-right: 2px;
      }

      .ant-tabs-tab-active {
        background: #fff;
      }

      .ant-tabs-ink-bar {
        display: none !important;
      }
    }

    .searchOrg {
      margin: 20px;
      // width: 360px;
      background: #f7f8f9;
    }

    .orgContainer {
      position: relative;
      overflow: auto;
      background: #f7f8f9;
      min-height: 400px;
      max-height: 600px;
    }
  }

  .right {
    flex: 1;
    margin-left: 10px;
    background: #fff;
    padding-bottom: 20px;
    position: relative;
    padding: 20px;

    .cadre-header {
      .header-search {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 20px;
        gap: 10px 10px;

        .header-item {
          .h-i-name {
            white-space: nowrap;
          }
        }

        div {
          display: flex;
          align-items: center;
        }

        .search-span {
          height: 100%;
          padding: 0px 10px;
          background: #fff;
          border: 1px solid #cdcdcd;
          margin-right: 5px;
          white-space: nowrap;
          cursor: pointer;
        }

        .search-active {
          height: 100%;
          padding: 0px 10px;
          border: 1px solid #cdcdcd;
          margin-right: 5px;
          cursor: pointer;
          color: #fff;
          background: #a4adb3;
          white-space: nowrap;
        }

        .search-btn {
          div {
            width: 80px;
            height: 30px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #1fa1fd;
            color: #fff;
            cursor: pointer;
          }

          .reset {
            color: #828a96;
            background: #f7f8fa;
          }
        }
      }

      .header-btn {
        width: 80px;
        height: 30px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #1fa1fd;
        color: #fff;
        cursor: pointer;

        i {
          margin-right: 10px;
        }
      }

      .ant-input,
      .ant-select-selection {
        border-radius: 6px;
      }
    }

    .cadre-content {
      margin-top: 10px;

      .handle {
        display: flex;
        justify-content: space-evenly;
      }
    }
  }

  .button-box {
    display: flex;
    gap: 0px 30px;
  }

  .right-box {
    width: 310px;
    margin-left: 10px;
    background: #f7f8f9;
    padding-bottom: 20px;
    position: relative;
    padding: 20px;
    height: 600px;

    .right-box-header {
      font-size: 16px;
      color: #1fa1fd;

      > span:first-of-type {
        display: inline-block;
        margin-right: 10px;
      }
    }

    .right-box-content {
      margin-top: 25px;
      height: 500px;
      overflow: auto;
      .right-box-item {
        height: auto;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        margin-bottom: 10px;

        .right-box-item-left {
          font-size: 14px;
          width: 270px;
          color: #000;

          > div:first-of-type {
            margin-bottom: 5px;
          }
        }

        .icon {
          font-size: 12px;
          color: white;
          cursor: pointer;
          width: 14px;
          height: 14px;
          padding-bottom: 3px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
        }
      }
    }
  }
}

.dr-modal-box {
  .custom-box {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .job-type {
      width: 300px;

      .ant-row {
        margin-bottom: 0px !important;
      }
    }
  }
}
.footer-btn {
  margin-top: 10px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
